import React from 'react';
import { SupportedFormat } from '../../types';

export interface QualitySliderProps {
  quality: number;
  onQualityChange: (quality: number) => void;
  disabled?: boolean;
  format: SupportedFormat;
  className?: string;
}

const QualitySlider: React.FC<QualitySliderProps> = ({
  quality,
  onQualityChange,
  disabled = false,
  format,
  className = '',
}) => {
  // Some formats don't support quality settings
  const supportsQuality = format === 'jpeg' || format === 'webp';
  
  if (!supportsQuality) {
    return (
      <div className={`space-y-2 ${className}`}>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Quality
        </label>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Quality setting not available for {format.toUpperCase()} format
        </div>
      </div>
    );
  }
  
  const percentage = Math.round(quality * 100);
  
  const getQualityLabel = (value: number) => {
    if (value >= 0.9) return 'Excellent';
    if (value >= 0.8) return 'High';
    if (value >= 0.6) return 'Medium';
    if (value >= 0.4) return 'Low';
    return 'Very Low';
  };
  
  const getQualityColor = (value: number) => {
    if (value >= 0.8) return 'text-green-600 dark:text-green-400';
    if (value >= 0.6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };
  
  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex justify-between items-center">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Quality
        </label>
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {percentage}%
          </span>
          <span className={`text-xs font-medium ${getQualityColor(quality)}`}>
            {getQualityLabel(quality)}
          </span>
        </div>
      </div>
      
      <div className="relative">
        <input
          type="range"
          min="0.1"
          max="1"
          step="0.05"
          value={quality}
          onChange={(e) => onQualityChange(parseFloat(e.target.value))}
          disabled={disabled}
          className={`
            w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer
            slider-thumb:appearance-none slider-thumb:w-4 slider-thumb:h-4 slider-thumb:rounded-full
            slider-thumb:bg-primary-600 slider-thumb:cursor-pointer slider-thumb:border-0
            slider-thumb:shadow-md slider-thumb:transition-all slider-thumb:duration-200
            hover:slider-thumb:bg-primary-700 focus:slider-thumb:ring-2 focus:slider-thumb:ring-primary-500
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
          style={{
            background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${percentage}%, #e5e7eb ${percentage}%, #e5e7eb 100%)`,
          }}
        />
        
        {/* Quality markers */}
        <div className="flex justify-between text-xs text-gray-400 dark:text-gray-500 mt-1">
          <span>Low</span>
          <span>Medium</span>
          <span>High</span>
          <span>Max</span>
        </div>
      </div>
      
      <div className="text-xs text-gray-500 dark:text-gray-400">
        Higher quality results in larger file sizes but better image clarity
      </div>
    </div>
  );
};

export default QualitySlider;
